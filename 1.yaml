port: 7890
socks-port: 7891
allow-lan: true
mode: rule
log-level: warning
external-controller: 0.0.0.0:9090

dns:
  enable: true
  listen: 0.0.0.0:53
  enhanced-mode: fake-ip
  nameserver:
    - tls://*******
    - tls://*******
  fallback:
    - tls://*******
    - tls://*******
  fallback-filter:
    geoip: true
    geoip-code: CN
    domain:
      - +.google.com
      - +.clients6.google.com
      - +.scholar.google.com
      - +.youtube.com
      - +.openai.com
      - +.facebook.com
  fake-ip-filter:
    # 防止 fake-ip 弄错局域网设备
    - '*.lan'
    - localhost
    - 'ntp.org'
    - 'pool.ntp.org'

proxies:
  - name: "lw-微软云东京"
    type: vless
    server: ************
    port: 40888
    uuid: e49e7526-cf2b-4711-bd30-2074de4d77a6
    network: tcp
    tls: true
    client-fingerprint: chrome
    reality-opts:
      public-key: 68xaGPbX59nT6VpcqY9quM8T-M-sWSZGBbzqB01wtXE
      short-id: "20"
    servername: www.microsoft.com

  - name: "linwu-香港"
    type: vless
    server: **************
    port: 40888
    uuid: 2f9259f8-653a-462e-9d1c-851b3a86b9db
    network: tcp
    tls: true
    client-fingerprint: chrome
    reality-opts:
      public-key: lKbXaAFskiiZo_4_ha39BeXs56Qzlw0UxJMBFoJNKTM
      short-id: "276e92"
    servername: www.microsoft.com

  - name: "lw-新加坡云"
    type: vless
    server: **************
    port: 40888
    uuid: dd6f7532-b85f-45bb-82c2-1311e0f2826c
    network: tcp
    tls: true
    client-fingerprint: chrome
    reality-opts:
      public-key: V8o4TJKRdRXvB3-ZzfLVNtFfqaMe9imyPTukno353Ds
      short-id: "043e03b328"
    servername: www.microsoft.com

proxy-groups:
  - name: "通用服务"
    type: select
    proxies:
      - lw-微软云东京
      - lw-新加坡云
      - linwu-香港

  - name: "AI服务"
    type: select
    proxies:
      - lw-微软云东京
      - lw-新加坡云

  - name: "视频服务"
    type: select
    proxies:
      - linwu-香港

  - name: "广告拦截"
    type: select
    proxies:
      - REJECT
      - DIRECT
    
rule-providers:
  ACL4SSR-Default:
    type: http
    behavior: classical
    path: ./ruleset/ACL4SSR-Default.yaml
    url: https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/ClashRules/Default.yaml
    interval: 86400

  ACL4SSR-Global:
    type: http
    behavior: classical
    path: ./ruleset/ACL4SSR-Global.yaml
    url: https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/ClashRules/Global.yaml
    interval: 86400

  AdBlock:
    type: http
    behavior: classical
    path: ./ruleset/AdBlock.yaml
    url: https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/ClashRules/ADBlock.yaml
    interval: 86400

rules:
  # ✅ 广告屏蔽规则
  - RULE-SET,AdBlock,REJECT

  # ✅ OpenAI / ChatGPT / Gemini 走 GPT专用（美国）
  - DOMAIN-SUFFIX,openai.com,AI服务
  - DOMAIN-SUFFIX,chatgpt.com,AI服务
  - DOMAIN-SUFFIX,api.openai.com,AI服务
  - DOMAIN-SUFFIX,auth0.com,AI服务
  - DOMAIN-SUFFIX,openaiapi-site.azureedge.net,AI服务
  - DOMAIN-KEYWORD,gemini,AI服务
  - DOMAIN-SUFFIX,bard.google.com,AI服务
  - DOMAIN-SUFFIX,googleusercontent.com,AI服务
  - DOMAIN-SUFFIX,gstatic.com,AI服务
  - DOMAIN-SUFFIX,ai.google.dev,AI服务
  - DOMAIN-SUFFIX,aiplatform.googleapis.com,AI服务

  # ✅ 默认规则
  - RULE-SET,ACL4SSR-Default,通用服务
  - RULE-SET,ACL4SSR-Global,通用服务
  - GEOIP,CN,DIRECT
  - MATCH,通用服务